class_name WallPhaseAbility
extends Ability

var _paint_component: PaintComponent
var _movement_component: MovementComponent
var _movement_system: MovementSystem
var _tile_query_system: TileQuerySystem
var _is_phasing: bool = false

func initialize(p_owner_node: Node2D) -> void:
	owner_node = p_owner_node
	_movement_component = _find_in_owner(&"MovementComponent")
	_paint_component = _find_in_owner(&"PaintComponent")
	_movement_system = _find_in_root(&"MovementSystem")
	_tile_query_system = _find_in_root(&"TileQuerySystem")
	if is_instance_valid(_movement_component):
		_movement_component.movement_blocked.connect(_on_movement_blocked)

func _exit_tree() -> void:
	if is_instance_valid(_movement_component):
		_movement_component.movement_blocked.disconnect(_on_movement_blocked)

func _on_movement_blocked(direction: Vector2) -> void:
	var phase_data := data as WallPhaseAbilityData
	if phase_data == null:
		return
	if _is_phasing:
		return
	if _paint_component == null or _paint_component.current_paint < phase_data.paint_cost:
		return
	var target_tile: Node2D = _find_passable_tile(direction)
	if not is_instance_valid(target_tile):
		return
	_paint_component.current_paint -= phase_data.paint_cost
	_is_phasing = true
	_movement_system.teleport(_movement_component, target_tile.global_position)
	_movement_component.movement_completed.connect(func() -> void:
		_is_phasing = false, CONNECT_ONE_SHOT)

func _find_passable_tile(direction: Vector2) -> Node2D:
	if _movement_component == null:
		return null
	var step: int = _movement_component.data.tile_size
	var pos: Vector2 = owner_node.global_position + direction * step
	var limit: int = 64
	while limit > 0:
		var tile: Node2D = _tile_query_system.get_tile_at_global_pos(pos)
		if is_instance_valid(tile):
			return tile
		pos += direction * step
		limit -= 1
	return null
